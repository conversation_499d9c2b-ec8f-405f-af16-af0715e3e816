<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 تشخيص Bug Bounty v4.0 - التقرير الرئيسي والتقارير المنفصلة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .success { border-color: #28a745; background: #d4edda; }
        .error { border-color: #dc3545; background: #f8d7da; }
        .warning { border-color: #ffc107; background: #fff3cd; }
        .info { border-color: #17a2b8; background: #d1ecf1; }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .result.success { background: #d4edda; color: #155724; }
        .result.error { background: #f8d7da; color: #721c24; }
        .result.warning { background: #fff3cd; color: #856404; }
        .result.info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover { background: #0056b3; }
        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .score {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }
        .score.excellent { background: #d4edda; color: #155724; }
        .score.good { background: #fff3cd; color: #856404; }
        .score.poor { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 تشخيص شامل لـ Bug Bounty v4.0</h1>
            <p>فحص التقرير الرئيسي والتقارير المنفصلة</p>
        </div>

        <div class="section info">
            <h2>🎯 بدء التشخيص</h2>
            <button onclick="runFullDiagnostic()">🚀 تشغيل التشخيص الشامل</button>
            <button onclick="clearResults()">🧹 مسح النتائج</button>
        </div>

        <div id="results"></div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function runFullDiagnostic() {
            clearResults();
            log('🔍 بدء التشخيص الشامل لـ Bug Bounty v4.0...', 'info');
            
            let score = 0;
            let totalTests = 0;
            
            // 1. فحص توفر BugBountyCore
            totalTests++;
            log('📊 1. فحص توفر BugBountyCore...', 'info');
            if (typeof BugBountyCore !== 'undefined') {
                log('✅ BugBountyCore متاح', 'success');
                score++;
            } else {
                log('❌ BugBountyCore غير متاح', 'error');
                return;
            }

            // 2. إنشاء مثيل
            totalTests++;
            log('📊 2. إنشاء مثيل BugBountyCore...', 'info');
            let core;
            try {
                core = new BugBountyCore();
                log('✅ تم إنشاء مثيل BugBountyCore بنجاح', 'success');
                score++;
            } catch (error) {
                log(`❌ فشل إنشاء مثيل: ${error.message}`, 'error');
                return;
            }

            // 3. فحص دالة preloadTemplate الجديدة
            totalTests++;
            log('📊 3. فحص دالة preloadTemplate الجديدة...', 'info');
            if (typeof core.preloadTemplate === 'function') {
                log('✅ دالة preloadTemplate موجودة', 'success');
                score++;

                // اختبار تحميل القالب مسبقاً
                try {
                    const template = await core.preloadTemplate();
                    if (template && template.length > 100) {
                        log(`✅ تم تحميل القالب مسبقاً بنجاح (${template.length} حرف)`, 'success');
                    } else {
                        log('⚠️ القالب المحمل فارغ أو قصير', 'warning');
                    }
                } catch (error) {
                    log(`⚠️ خطأ في تحميل القالب مسبقاً: ${error.message}`, 'warning');
                }
            } else {
                log('❌ دالة preloadTemplate غير موجودة', 'error');
            }

            // 4. فحص دالة generateFinalComprehensiveReport
            totalTests++;
            log('📊 4. فحص دالة generateFinalComprehensiveReport (التقرير الرئيسي)...', 'info');
            if (typeof core.generateFinalComprehensiveReport === 'function') {
                log('✅ دالة generateFinalComprehensiveReport موجودة', 'success');
                score++;

                // اختبار الدالة
                totalTests++;
                log('📊 5. اختبار دالة generateFinalComprehensiveReport...', 'info');
                try {
                    const mockAnalysis = {
                        vulnerabilities: [],
                        total_vulnerabilities: 0,
                        pages_processed: 1,
                        scan_timestamp: new Date().toISOString()
                    };
                    
                    const report = await core.generateFinalComprehensiveReport(mockAnalysis, [], 'https://example.com');
                    
                    if (report && typeof report === 'string' && report.length > 100) {
                        log(`✅ التقرير الرئيسي يعمل بنجاح - الطول: ${report.length} حرف`, 'success');
                        score++;
                        
                        // فحص محتوى التقرير
                        if (report.includes('example.com')) {
                            log('✅ التقرير يحتوي على URL الهدف', 'success');
                        }
                        if (report.includes('<html>') || report.includes('<!DOCTYPE')) {
                            log('✅ التقرير بصيغة HTML صحيحة', 'success');
                        }
                    } else {
                        log(`❌ التقرير الرئيسي غير صالح - النوع: ${typeof report}, الطول: ${report ? report.length : 'null'}`, 'error');
                    }
                } catch (error) {
                    log(`❌ خطأ في التقرير الرئيسي: ${error.message}`, 'error');
                }
            } else {
                log('❌ دالة generateFinalComprehensiveReport غير موجودة', 'error');
            }

            // 6. فحص دالة generatePageHTMLReport
            totalTests++;
            log('📊 6. فحص دالة generatePageHTMLReport (التقارير المنفصلة)...', 'info');
            if (typeof core.generatePageHTMLReport === 'function') {
                log('✅ دالة generatePageHTMLReport موجودة', 'success');
                score++;

                // اختبار الدالة
                totalTests++;
                log('📊 7. اختبار دالة generatePageHTMLReport...', 'info');
                try {
                    const mockPageResult = {
                        vulnerabilities: [{
                            name: 'Test Vulnerability',
                            type: 'XSS',
                            severity: 'High'
                        }],
                        page_url: 'https://example.com/test'
                    };
                    
                    const pageReport = await core.generatePageHTMLReport(mockPageResult, 'https://example.com/test', 1);
                    
                    if (pageReport && typeof pageReport === 'string' && pageReport.length > 100) {
                        log(`✅ التقرير المنفصل يعمل بنجاح - الطول: ${pageReport.length} حرف`, 'success');
                        score++;
                        
                        // فحص محتوى التقرير
                        if (pageReport.includes('example.com')) {
                            log('✅ التقرير المنفصل يحتوي على URL الهدف', 'success');
                        }
                        if (pageReport.includes('<html>') || pageReport.includes('<!DOCTYPE')) {
                            log('✅ التقرير المنفصل بصيغة HTML صحيحة', 'success');
                        }
                    } else {
                        log(`❌ التقرير المنفصل غير صالح - النوع: ${typeof pageReport}, الطول: ${pageReport ? pageReport.length : 'null'}`, 'error');
                    }
                } catch (error) {
                    log(`❌ خطأ في التقرير المنفصل: ${error.message}`, 'error');
                }
            } else {
                log('❌ دالة generatePageHTMLReport غير موجودة', 'error');
            }

            // 8. فحص template loading
            totalTests++;
            log('📊 8. فحص تحميل القالب...', 'info');
            try {
                const response = await fetch('assets/modules/bugbounty/report_template.html');
                if (response.ok) {
                    const templateContent = await response.text();
                    if (templateContent && templateContent.length > 100) {
                        log(`✅ قالب التقرير يحمل بنجاح - الحجم: ${templateContent.length} حرف`, 'success');
                        score++;
                    } else {
                        log('❌ قالب التقرير فارغ أو قصير', 'error');
                    }
                } else {
                    log(`❌ فشل تحميل قالب التقرير: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ خطأ في تحميل القالب: ${error.message}`, 'error');
            }

            // النتيجة النهائية
            const percentage = Math.round((score / totalTests) * 100);
            log('', 'info'); // فاصل
            
            const scoreDiv = document.createElement('div');
            scoreDiv.className = `score ${percentage >= 80 ? 'excellent' : percentage >= 60 ? 'good' : 'poor'}`;
            scoreDiv.innerHTML = `
                <h2>🎯 النتيجة النهائية</h2>
                <p>النقاط: ${score}/${totalTests} (${percentage}%)</p>
            `;
            document.getElementById('results').appendChild(scoreDiv);

            if (percentage >= 80) {
                log('🎉 ممتاز! النظام يعمل بشكل صحيح - جاهز للاستخدام على localhost:3000', 'success');
            } else if (percentage >= 60) {
                log('⚠️ جيد - النظام يعمل مع بعض المشاكل الطفيفة', 'warning');
            } else {
                log('❌ ضعيف - النظام يحتاج إصلاحات جوهرية', 'error');
            }

            log('🏁 انتهى التشخيص الشامل', 'info');
        }
    </script>
</body>
</html>
