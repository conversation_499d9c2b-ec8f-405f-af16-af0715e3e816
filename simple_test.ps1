Write-Host "Testing toLowerCase fixes..." -ForegroundColor Yellow

$files = @(
    'assets\assistant-core.js',
    'assets\modules\suggestion.js', 
    'assets\modules\fileCreator\FileCreatorCore.js',
    'assets\modules\summarizer.js',
    'assets\modules\bugbounty\BugBountyCore.js'
)

$totalIssues = 0

foreach($file in $files) {
    if(Test-Path $file) {
        $content = Get-Content $file -Raw
        $totalMatches = [regex]::Matches($content, 'toLowerCase\(\)')
        
        Write-Host "$file : $($totalMatches.Count) toLowerCase calls" -ForegroundColor $(if($totalMatches.Count -lt 10) {"Green"} else {"Yellow"})
        
    } else {
        Write-Host "$file : File not found" -ForegroundColor Red
    }
}

Write-Host "`nTesting BugBountyCore export fix..." -ForegroundColor Yellow
if(Test-Path 'assets\modules\bugbounty\BugBountyCore.js') {
    $bugBountyContent = Get-Content 'assets\modules\bugbounty\BugBountyCore.js' -Raw
    
    $fallbackTemplate = $bugBountyContent -match "fallbackTemplate"
    
    if($fallbackTemplate) {
        Write-Host "Fallback template system found" -ForegroundColor Green
    } else {
        Write-Host "Fallback template system NOT found" -ForegroundColor Red
    }
}

Write-Host "`nRecommendations:" -ForegroundColor Cyan
Write-Host "1. Test system on localhost:3000" -ForegroundColor White
Write-Host "2. Try exporting separate reports" -ForegroundColor White
Write-Host "3. Check console for toLowerCase errors" -ForegroundColor White
