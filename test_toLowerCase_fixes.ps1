Write-Host "🔍 اختبار إصلاحات toLowerCase()..." -ForegroundColor Yellow

$files = @(
    'assets\assistant-core.js',
    'assets\modules\suggestion.js', 
    'assets\modules\fileCreator\FileCreatorCore.js',
    'assets\modules\summarizer.js',
    'assets\modules\bugbounty\BugBountyCore.js'
)

$totalIssues = 0

foreach($file in $files) {
    if(Test-Path $file) {
        $content = Get-Content $file -Raw
        
        # البحث عن استخدامات toLowerCase() غير الآمنة
        $unsafeMatches = [regex]::Matches($content, '(?<!typeof\s+\w+\s+===\s+.string.\s+\?\s+)\w+\.toLowerCase\(\)')
        $totalMatches = [regex]::Matches($content, 'toLowerCase\(\)')
        
        Write-Host "`n📁 $file" -ForegroundColor Cyan
        Write-Host "   إجمالي استخدامات toLowerCase(): $($totalMatches.Count)" -ForegroundColor White
        Write-Host "   استخدامات غير آمنة محتملة: $($unsafeMatches.Count)" -ForegroundColor $(if($unsafeMatches.Count -eq 0) {"Green"} else {"Red"})
        
        if($unsafeMatches.Count -gt 0) {
            $totalIssues += $unsafeMatches.Count
            Write-Host "   ⚠️ مواقع المشاكل المحتملة:" -ForegroundColor Yellow
            foreach($match in $unsafeMatches) {
                $lineNumber = ($content.Substring(0, $match.Index) -split "`n").Count
                Write-Host "      السطر $lineNumber : $($match.Value)" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "`n❌ $file : غير موجود" -ForegroundColor Red
    }
}

Write-Host "`n📊 النتيجة النهائية:" -ForegroundColor Yellow
if($totalIssues -eq 0) {
    Write-Host "✅ تم إصلاح جميع مشاكل toLowerCase() بنجاح!" -ForegroundColor Green
} else {
    Write-Host "❌ لا تزال هناك $totalIssues مشكلة تحتاج إصلاح" -ForegroundColor Red
}

Write-Host "`n🔍 اختبار مشكلة التصدير في BugBountyCore.js..." -ForegroundColor Yellow
if(Test-Path 'assets\modules\bugbounty\BugBountyCore.js') {
    $bugBountyContent = Get-Content 'assets\modules\bugbounty\BugBountyCore.js' -Raw
    
    # البحث عن رسالة الخطأ القديمة
    $oldErrorMessage = $bugBountyContent -match "throw new Error.*لا يوجد بديل"
    
    if($oldErrorMessage) {
        Write-Host "❌ لا تزال رسالة الخطأ القديمة موجودة" -ForegroundColor Red
    } else {
        Write-Host "✅ تم إزالة رسالة الخطأ القديمة" -ForegroundColor Green
    }
    
    # البحث عن القالب البديل
    $fallbackTemplate = $bugBountyContent -match "fallbackTemplate"
    
    if($fallbackTemplate) {
        Write-Host "✅ تم إضافة نظام القالب البديل" -ForegroundColor Green
    } else {
        Write-Host "❌ لم يتم العثور على نظام القالب البديل" -ForegroundColor Red
    }
}

Write-Host "`n🎯 التوصيات:" -ForegroundColor Cyan
Write-Host "1. اختبار النظام على localhost:3000" -ForegroundColor White
Write-Host "2. تجربة تصدير التقارير المنفصلة" -ForegroundColor White
Write-Host "3. التحقق من عدم ظهور أخطاء toLowerCase() في Console" -ForegroundColor White
