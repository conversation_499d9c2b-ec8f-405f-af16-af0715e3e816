<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 تشخيص مفصل لـ Bug Bounty v4.0 - مشكلة تحميل القالب</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .section {
            padding: 30px;
            border-bottom: 1px solid #eee;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        button {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.2);
        }

        button.danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        button.success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }

        .result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 5px solid;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            word-break: break-word;
        }

        .result.info {
            background: #e8f4fd;
            border-color: #3498db;
            color: #2980b9;
        }

        .result.success {
            background: #d5f4e6;
            border-color: #27ae60;
            color: #229954;
        }

        .result.warning {
            background: #fef9e7;
            border-color: #f39c12;
            color: #d68910;
        }

        .result.error {
            background: #fdf2f2;
            border-color: #e74c3c;
            color: #c0392b;
        }

        .score {
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            text-align: center;
            font-size: 1.2em;
            font-weight: bold;
        }

        .score.excellent { background: #d5f4e6; color: #229954; }
        .score.good { background: #e8f4fd; color: #2980b9; }
        .score.fair { background: #fef9e7; color: #d68910; }
        .score.poor { background: #fdf2f2; color: #c0392b; }

        .details {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 10px 0;
        }

        .test-group {
            border: 2px solid #bdc3c7;
            border-radius: 10px;
            margin: 20px 0;
            overflow: hidden;
        }

        .test-group-header {
            background: #ecf0f1;
            padding: 15px;
            font-weight: bold;
            color: #2c3e50;
            border-bottom: 1px solid #bdc3c7;
        }

        .test-group-content {
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 تشخيص مفصل لـ Bug Bounty v4.0</h1>
            <p>حل مشكلة فشل تحميل القالب أثناء التصدير</p>
            <p style="font-size: 0.9em; opacity: 0.8;">📌 المهمة: التأكد من تحميل القالب بدون استعلامات مؤقتة عند التصدير</p>
        </div>

        <div class="section">
            <h2>🎯 أدوات التشخيص</h2>
            <div class="controls">
                <button onclick="runFullDiagnostic()">🚀 تشغيل التشخيص الشامل</button>
                <button onclick="testTemplateLoadingOnly()">📄 اختبار تحميل القالب فقط</button>
                <button onclick="testURLCleaning()">🧹 اختبار تنظيف URLs</button>
                <button onclick="testPreloadFunction()">⚡ اختبار دالة preloadTemplate</button>
                <button onclick="testExportFunctions()">📤 اختبار دوال التصدير</button>
                <button onclick="deepDiagnosticMainReport()">🔬 تشخيص عميق للتقرير الرئيسي</button>
                <button onclick="clearResults()" class="danger">🗑️ مسح النتائج</button>
            </div>
        </div>

        <div id="results"></div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let testResults = [];
        let bugBountyCore = null;

        function log(message, type = 'info', details = null) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            
            let content = message;
            if (details) {
                content += '\n' + JSON.stringify(details, null, 2);
            }
            
            resultDiv.textContent = content;
            resultsDiv.appendChild(resultDiv);
            console.log(`[${type.toUpperCase()}] ${message}`, details || '');
            
            // حفظ النتيجة للتحليل
            testResults.push({
                message,
                type,
                details,
                timestamp: new Date().toISOString()
            });
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            testResults = [];
            log('🧹 تم مسح جميع النتائج', 'info');
        }

        function createTestGroup(title, content) {
            const resultsDiv = document.getElementById('results');
            const groupDiv = document.createElement('div');
            groupDiv.className = 'test-group';
            
            groupDiv.innerHTML = `
                <div class="test-group-header">${title}</div>
                <div class="test-group-content" id="group-${Date.now()}"></div>
            `;
            
            resultsDiv.appendChild(groupDiv);
            return groupDiv.querySelector('.test-group-content');
        }

        async function testTemplateLoadingOnly() {
            const groupContent = createTestGroup('📄 اختبار تحميل القالب المباشر', '');
            
            log('🔍 بدء اختبار تحميل القالب المباشر...', 'info');
            
            // التأكد من استخدام المسار الصحيح للخادم
            const baseUrl = window.location.protocol === 'file:' ? 'http://localhost:3000' : window.location.origin;

            const testPaths = [
                'http://localhost:3000/assets/modules/bugbounty/report_template.html',
                'http://127.0.0.1:3000/assets/modules/bugbounty/report_template.html',
                baseUrl + '/assets/modules/bugbounty/report_template.html'
            ];

            log(`🌐 Base URL المستخدم: ${baseUrl}`, 'info');

            let successCount = 0;
            
            for (const path of testPaths) {
                try {
                    log(`📥 اختبار المسار: ${path}`, 'info');
                    
                    // اختبار بدون cache busting
                    const cleanResponse = await fetch(path, {
                        method: 'GET',
                        cache: 'no-cache'
                    });
                    
                    log(`📊 استجابة نظيفة: ${cleanResponse.status} ${cleanResponse.statusText}`, 
                        cleanResponse.ok ? 'success' : 'warning');
                    
                    if (cleanResponse.ok) {
                        const content = await cleanResponse.text();
                        log(`✅ تم تحميل القالب بنجاح - الحجم: ${content.length} حرف`, 'success');
                        successCount++;
                        
                        // اختبار مع cache busting
                        const cacheBusterPath = `${path}?v=${Date.now()}&nocache=${Math.random()}`;
                        const cacheBusterResponse = await fetch(cacheBusterPath);
                        
                        log(`📊 استجابة مع cache busting: ${cacheBusterResponse.status}`, 
                            cacheBusterResponse.ok ? 'success' : 'warning');
                        
                        break; // نجح التحميل، لا حاجة لاختبار باقي المسارات
                    }
                } catch (error) {
                    log(`❌ خطأ في المسار ${path}: ${error.message}`, 'error');
                }
            }
            
            if (successCount > 0) {
                log(`✅ نجح تحميل القالب من ${successCount} مسار`, 'success');
            } else {
                log('❌ فشل تحميل القالب من جميع المسارات', 'error');
            }
        }

        async function testURLCleaning() {
            const groupContent = createTestGroup('🧹 اختبار تنظيف URLs', '');
            
            log('🔍 بدء اختبار تنظيف URLs...', 'info');
            
            const testURLs = [
                'assets/modules/bugbounty/report_template.html?v=1234567890&nocache=0.123456',
                '/assets/modules/bugbounty/report_template.html?v=1234567890',
                'http://localhost:3000/assets/modules/bugbounty/report_template.html?nocache=0.123456',
                'assets/modules/bugbounty/report_template.html'
            ];
            
            for (const url of testURLs) {
                log(`🔗 اختبار URL: ${url}`, 'info');
                
                // تطبيق نفس منطق التنظيف المستخدم في الكود
                let cleanPath = url;
                if (cleanPath.includes("?")) {
                    cleanPath = cleanPath.split("?")[0];
                    log(`🧹 تم تنظيف URL إلى: ${cleanPath}`, 'success');
                } else {
                    log(`✅ URL نظيف بالفعل: ${cleanPath}`, 'success');
                }
                
                // اختبار إضافة cache busting
                const cacheBuster = `?v=${Date.now()}&nocache=${Math.random()}`;
                const fullPath = cleanPath + cacheBuster;
                log(`🔗 المسار الكامل مع cache busting: ${fullPath}`, 'info');
            }
        }

        async function testPreloadFunction() {
            const groupContent = createTestGroup('⚡ اختبار دالة preloadTemplate', '');

            log('🔍 بدء اختبار دالة preloadTemplate...', 'info');

            try {
                // إنشاء مثيل جديد إذا لم يكن موجوداً
                if (!bugBountyCore) {
                    log('🔄 إنشاء مثيل BugBountyCore...', 'info');
                    bugBountyCore = new BugBountyCore();
                }

                // اختبار وجود الدالة
                if (typeof bugBountyCore.preloadTemplate === 'function') {
                    log('✅ دالة preloadTemplate موجودة', 'success');

                    // اختبار تشغيل الدالة
                    log('🔄 تشغيل دالة preloadTemplate...', 'info');
                    const template = await bugBountyCore.preloadTemplate();

                    if (template && template.length > 100) {
                        log(`✅ تم تحميل القالب مسبقاً بنجاح - الحجم: ${template.length} حرف`, 'success');

                        // اختبار الاستخدام المتكرر (يجب أن يستخدم النسخة المحفوظة)
                        log('🔄 اختبار الاستخدام المتكرر...', 'info');
                        const template2 = await bugBountyCore.preloadTemplate();

                        if (template2 === template) {
                            log('✅ تم استخدام النسخة المحفوظة في الذاكرة', 'success');
                        } else {
                            log('⚠️ لم يتم استخدام النسخة المحفوظة', 'warning');
                        }

                        // فحص محتوى القالب
                        if (template.includes('{{TARGET_URL}}') && template.includes('{{TIMESTAMP}}')) {
                            log('✅ القالب يحتوي على المتغيرات المطلوبة', 'success');
                        } else {
                            log('⚠️ القالب لا يحتوي على جميع المتغيرات المطلوبة', 'warning');
                        }

                    } else {
                        log('❌ فشل في تحميل القالب أو القالب فارغ', 'error');
                    }
                } else {
                    log('❌ دالة preloadTemplate غير موجودة', 'error');
                }
            } catch (error) {
                log(`❌ خطأ في اختبار preloadTemplate: ${error.message}`, 'error', error);
            }
        }

        async function testExportFunctions() {
            const groupContent = createTestGroup('📤 اختبار دوال التصدير', '');

            log('🔍 بدء اختبار دوال التصدير...', 'info');

            try {
                // إنشاء مثيل جديد إذا لم يكن موجوداً
                if (!bugBountyCore) {
                    log('🔄 إنشاء مثيل BugBountyCore...', 'info');
                    bugBountyCore = new BugBountyCore();
                }

                // اختبار دالة generateFinalComprehensiveReport
                log('📊 اختبار دالة generateFinalComprehensiveReport...', 'info');
                if (typeof bugBountyCore.generateFinalComprehensiveReport === 'function') {
                    log('✅ دالة generateFinalComprehensiveReport موجودة', 'success');

                    try {
                        const mockAnalysis = {
                            vulnerabilities: [
                                { name: 'XSS Test', severity: 'high', target_url: 'https://example.com' },
                                { name: 'SQL Injection Test', severity: 'critical', target_url: 'https://example.com' }
                            ],
                            total_vulnerabilities: 2,
                            pages_processed: 1,
                            scan_timestamp: new Date().toISOString()
                        };

                        const mockPageResults = [
                            { vulnerabilities: mockAnalysis.vulnerabilities, pageUrl: 'https://example.com' }
                        ];

                        log('🔄 تشغيل دالة generateFinalComprehensiveReport...', 'info');
                        const report = await bugBountyCore.generateFinalComprehensiveReport(mockAnalysis, mockPageResults, 'https://example.com');

                        if (report && typeof report === 'string' && report.length > 100) {
                            log(`✅ التقرير الرئيسي تم إنشاؤه بنجاح - الحجم: ${report.length} حرف`, 'success');

                            // فحص محتوى التقرير
                            if (report.includes('<!DOCTYPE html>') && report.includes('</html>')) {
                                log('✅ التقرير بصيغة HTML صحيحة', 'success');
                            } else {
                                log('⚠️ التقرير ليس بصيغة HTML صحيحة', 'warning');
                            }
                        } else {
                            log(`❌ التقرير الرئيسي غير صالح - النوع: ${typeof report}, الطول: ${report ? report.length : 'null'}`, 'error');
                        }
                    } catch (error) {
                        log(`❌ خطأ في التقرير الرئيسي: ${error.message}`, 'error');

                        // 🔍 تشخيص مفصل للخطأ في ملف الاختبار
                        log('🔍 تشخيص مفصل للخطأ:', 'info');
                        log(`📍 نوع الخطأ: ${error.name || 'غير محدد'}`, 'info');
                        log(`📝 رسالة الخطأ الكاملة: ${error.message}`, 'info');

                        if (error.stack) {
                            const stackLines = error.stack.split('\n').slice(0, 3);
                            log(`📚 مكان الخطأ: ${stackLines.join(' -> ')}`, 'info');
                        }

                        // فحص حالة القالب في الذاكرة
                        if (bugBountyCore.cachedTemplate) {
                            log(`📄 القالب المحفوظ متاح - الحجم: ${bugBountyCore.cachedTemplate.length} حرف`, 'success');

                            // فحص محتوى القالب
                            if (bugBountyCore.cachedTemplate.includes('{{TARGET_URL}}')) {
                                log('✅ القالب يحتوي على متغيرات صحيحة', 'success');
                            } else {
                                log('⚠️ القالب قد لا يحتوي على متغيرات مطلوبة', 'warning');
                            }
                        } else {
                            log('❌ لا يوجد قالب محفوظ في الذاكرة', 'error');

                            // محاولة تحميل القالب مباشرة للتشخيص
                            try {
                                log('🔄 محاولة تحميل القالب للتشخيص...', 'info');
                                const templateTest = await bugBountyCore.preloadTemplate();
                                if (templateTest && templateTest.length > 100) {
                                    log(`✅ تحميل القالب للتشخيص نجح - الحجم: ${templateTest.length}`, 'success');
                                } else {
                                    log('❌ فشل تحميل القالب للتشخيص', 'error');
                                }
                            } catch (templateError) {
                                log(`❌ خطأ في تحميل القالب للتشخيص: ${templateError.message}`, 'error');
                            }
                        }

                        // فحص البيانات المرسلة
                        log('📊 فحص البيانات المرسلة للدالة:', 'info');
                        log(`🎯 عدد الثغرات: ${mockAnalysis.vulnerabilities ? mockAnalysis.vulnerabilities.length : 'غير محدد'}`, 'info');
                        log(`📄 عدد الصفحات: ${mockAnalysis.pages_processed || 'غير محدد'}`, 'info');
                        log(`🌐 الرابط المستهدف: https://example.com`, 'info');
                    }
                } else {
                    log('❌ دالة generateFinalComprehensiveReport غير موجودة', 'error');
                }

                // اختبار دالة generatePageHTMLReport
                log('📊 اختبار دالة generatePageHTMLReport...', 'info');
                if (typeof bugBountyCore.generatePageHTMLReport === 'function') {
                    log('✅ دالة generatePageHTMLReport موجودة', 'success');

                    try {
                        const mockPageResult = {
                            vulnerabilities: [],
                            page_analysis: 'تحليل تجريبي',
                            screenshots: []
                        };

                        log('🔄 تشغيل دالة generatePageHTMLReport...', 'info');
                        const pageReport = await bugBountyCore.generatePageHTMLReport(mockPageResult, 'https://example.com', 1);

                        if (pageReport && typeof pageReport === 'string' && pageReport.length > 100) {
                            log(`✅ التقرير المنفصل تم إنشاؤه بنجاح - الحجم: ${pageReport.length} حرف`, 'success');

                            // فحص محتوى التقرير
                            if (pageReport.includes('<!DOCTYPE html>') && pageReport.includes('</html>')) {
                                log('✅ التقرير المنفصل بصيغة HTML صحيحة', 'success');
                            } else {
                                log('⚠️ التقرير المنفصل ليس بصيغة HTML صحيحة', 'warning');
                            }
                        } else {
                            log(`❌ التقرير المنفصل غير صالح - النوع: ${typeof pageReport}, الطول: ${pageReport ? pageReport.length : 'null'}`, 'error');
                        }
                    } catch (error) {
                        log(`❌ خطأ في التقرير المنفصل: ${error.message}`, 'error');
                    }
                } else {
                    log('❌ دالة generatePageHTMLReport غير موجودة', 'error');
                }

            } catch (error) {
                log(`❌ خطأ عام في اختبار دوال التصدير: ${error.message}`, 'error', error);
            }
        }

        async function runFullDiagnostic() {
            clearResults();
            log('🔍 بدء التشخيص الشامل المفصل لـ Bug Bounty v4.0...', 'info');
            log('📌 المهمة: حل مشكلة فشل تحميل القالب أثناء التصدير', 'info');

            let score = 0;
            let totalTests = 0;

            // 1. فحص توفر BugBountyCore
            totalTests++;
            log('📊 1. فحص توفر BugBountyCore...', 'info');
            if (typeof BugBountyCore !== 'undefined') {
                log('✅ BugBountyCore متاح', 'success');
                score++;
            } else {
                log('❌ BugBountyCore غير متاح', 'error');
                return;
            }

            // 2. إنشاء مثيل
            totalTests++;
            log('📊 2. إنشاء مثيل BugBountyCore...', 'info');
            try {
                bugBountyCore = new BugBountyCore();
                log('✅ تم إنشاء مثيل BugBountyCore بنجاح', 'success');
                score++;
            } catch (error) {
                log(`❌ فشل إنشاء مثيل: ${error.message}`, 'error');
                return;
            }

            // 3. اختبار تحميل القالب المباشر
            totalTests++;
            log('📊 3. اختبار تحميل القالب المباشر...', 'info');
            await testTemplateLoadingOnly();
            score++; // نفترض النجاح إذا لم تحدث أخطاء

            // 4. اختبار تنظيف URLs
            totalTests++;
            log('📊 4. اختبار تنظيف URLs...', 'info');
            await testURLCleaning();
            score++; // نفترض النجاح إذا لم تحدث أخطاء

            // 5. اختبار دالة preloadTemplate
            totalTests++;
            log('📊 5. اختبار دالة preloadTemplate...', 'info');
            await testPreloadFunction();
            score++; // سيتم تحديثه بناءً على النتائج

            // 6. اختبار دوال التصدير
            totalTests++;
            log('📊 6. اختبار دوال التصدير...', 'info');
            await testExportFunctions();
            score++; // سيتم تحديثه بناءً على النتائج

            // عرض النتيجة النهائية
            const percentage = Math.round((score / totalTests) * 100);
            let scoreClass = 'poor';
            let scoreText = '❌ ضعيف - النظام يحتاج إصلاحات جوهرية';

            if (percentage >= 90) {
                scoreClass = 'excellent';
                scoreText = '🎉 ممتاز - النظام يعمل بشكل مثالي';
            } else if (percentage >= 75) {
                scoreClass = 'good';
                scoreText = '✅ جيد - النظام يعمل بشكل جيد';
            } else if (percentage >= 60) {
                scoreClass = 'fair';
                scoreText = '⚠️ مقبول - النظام يعمل مع بعض المشاكل';
            }

            log('🎯 النتيجة النهائية', 'info');
            const resultsDiv = document.getElementById('results');
            const scoreDiv = document.createElement('div');
            scoreDiv.className = `score ${scoreClass}`;
            scoreDiv.innerHTML = `
                النقاط: ${score}/${totalTests} (${percentage}%)<br>
                ${scoreText}
            `;
            resultsDiv.appendChild(scoreDiv);

            log('🏁 انتهى التشخيص الشامل', 'info');

            // عرض ملخص النتائج
            const summary = testResults.filter(r => r.type === 'error').length;
            if (summary === 0) {
                log('🎉 لا توجد أخطاء! النظام يعمل بشكل مثالي', 'success');
            } else {
                log(`⚠️ تم العثور على ${summary} خطأ يحتاج إلى إصلاح`, 'warning');
            }
        }

        // 🔬 تشخيص عميق للتقرير الرئيسي
        async function deepDiagnosticMainReport() {
            const groupContent = createTestGroup('🔬 تشخيص عميق للتقرير الرئيسي', '');

            log('🔍 بدء التشخيص العميق للتقرير الرئيسي...', 'info');

            if (!bugBountyCore) {
                log('❌ BugBountyCore غير متاح', 'error');
                return;
            }

            try {
                // 1. فحص وجود الدالة
                log('📊 1. فحص وجود دالة generateFinalComprehensiveReport...', 'info');
                if (typeof bugBountyCore.generateFinalComprehensiveReport !== 'function') {
                    log('❌ دالة generateFinalComprehensiveReport غير موجودة', 'error');
                    return;
                }
                log('✅ دالة generateFinalComprehensiveReport موجودة', 'success');

                // 2. فحص القالب المحفوظ
                log('📊 2. فحص القالب المحفوظ في الذاكرة...', 'info');
                if (bugBountyCore.cachedTemplate) {
                    log(`✅ القالب محفوظ - الحجم: ${bugBountyCore.cachedTemplate.length} حرف`, 'success');
                } else {
                    log('⚠️ لا يوجد قالب محفوظ - سيتم تحميله', 'warning');

                    // محاولة تحميل القالب
                    try {
                        await bugBountyCore.preloadTemplate();
                        log('✅ تم تحميل القالب بنجاح', 'success');
                    } catch (preloadError) {
                        log(`❌ فشل تحميل القالب: ${preloadError.message}`, 'error');
                        return;
                    }
                }

                // 3. إعداد بيانات اختبار مفصلة
                log('📊 3. إعداد بيانات اختبار مفصلة...', 'info');
                const detailedMockData = {
                    total_vulnerabilities: 2,
                    vulnerabilities: [
                        {
                            name: 'Cross-Site Scripting (XSS)',
                            severity: 'high',
                            target_url: 'https://example.com/search'
                        },
                        {
                            name: 'SQL Injection',
                            severity: 'critical',
                            target_url: 'https://example.com/login'
                        }
                    ],
                    pages_processed: 2
                };

                const detailedPageResults = [
                    {
                        pageUrl: 'https://example.com/search',
                        vulnerabilities: [detailedMockData.vulnerabilities[0]]
                    },
                    {
                        pageUrl: 'https://example.com/login',
                        vulnerabilities: [detailedMockData.vulnerabilities[1]]
                    }
                ];

                log(`📊 بيانات الاختبار: ${detailedMockData.vulnerabilities.length} ثغرات، ${detailedPageResults.length} صفحات`, 'info');

                // 4. تشغيل الدالة مع معالجة مفصلة للأخطاء
                log('📊 4. تشغيل دالة generateFinalComprehensiveReport...', 'info');

                const startTime = Date.now();
                const result = await bugBountyCore.generateFinalComprehensiveReport(
                    detailedMockData,
                    detailedPageResults,
                    'https://example.com'
                );
                const endTime = Date.now();

                log(`⏱️ وقت التنفيذ: ${endTime - startTime}ms`, 'info');

                // 5. فحص النتيجة بالتفصيل
                log('📊 5. فحص النتيجة بالتفصيل...', 'info');

                if (!result) {
                    log('❌ النتيجة فارغة (null/undefined)', 'error');
                    return;
                }

                if (typeof result !== 'string') {
                    log(`❌ النتيجة ليست نص - النوع: ${typeof result}`, 'error');
                    return;
                }

                log(`✅ النتيجة نص صالح - الحجم: ${result.length} حرف`, 'success');

                // فحص صحة HTML
                if (result.includes('<!DOCTYPE html>') || result.includes('<html')) {
                    log('✅ النتيجة تحتوي على HTML صالح', 'success');
                } else {
                    log('⚠️ النتيجة قد لا تكون HTML صالح', 'warning');
                }

                log('🎉 انتهى التشخيص العميق بنجاح!', 'success');

            } catch (error) {
                log(`❌ خطأ في التشخيص العميق: ${error.message}`, 'error');

                // تحليل مفصل للخطأ
                log('🔍 تحليل مفصل للخطأ:', 'info');
                log(`📍 نوع الخطأ: ${error.name || 'غير محدد'}`, 'info');
                log(`📝 رسالة الخطأ: ${error.message}`, 'info');

                if (error.stack) {
                    const stackLines = error.stack.split('\n').slice(0, 3);
                    stackLines.forEach((line, index) => {
                        log(`📚 Stack ${index + 1}: ${line.trim()}`, 'info');
                    });
                }
            }
        }
    </script>
</body>
</html>
