<!DOCTYPE html>
<html>
<head>
    <title>اختبار نهائي - Bug Bounty Template Loading</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #005a87; }
    </style>
</head>
<body>
    <h1>🛡️ اختبار نهائي - Bug Bounty Template Loading</h1>
    <div id="output" class="log"></div>
    <button onclick="runFullTest()">🚀 تشغيل الاختبار الكامل</button>
    <button onclick="clearOutput()">🧹 مسح السجل</button>
    
    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const className = type;
            output.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function clearOutput() {
            document.getElementById('output').innerHTML = '';
        }

        async function runFullTest() {
            try {
                clearOutput();
                log('🔄 بدء الاختبار الكامل...', 'info');
                
                // 1. التحقق من توفر BugBountyCore
                if (typeof BugBountyCore === 'undefined') {
                    log('❌ BugBountyCore غير متاح', 'error');
                    return;
                }
                log('✅ BugBountyCore متاح', 'success');
                
                // 2. إنشاء مثيل
                const core = new BugBountyCore();
                log('✅ تم إنشاء مثيل BugBountyCore', 'success');
                
                // 3. التحقق من دالة preloadTemplate
                if (typeof core.preloadTemplate !== 'function') {
                    log('❌ دالة preloadTemplate غير متاحة', 'error');
                    return;
                }
                log('✅ دالة preloadTemplate متاحة', 'success');
                
                // 4. اختبار تحميل القالب
                log('🔄 اختبار تحميل القالب...', 'info');
                core.cachedTemplate = null; // مسح القالب للاختبار
                
                await core.preloadTemplate();
                
                if (core.cachedTemplate && core.cachedTemplate.length > 0) {
                    log(`✅ تم تحميل القالب بنجاح - الطول: ${core.cachedTemplate.length} حرف`, 'success');
                } else {
                    log('❌ فشل في تحميل القالب', 'error');
                    return;
                }
                
                // 5. اختبار التصدير مع بيانات وهمية بسيطة
                log('🔄 اختبار التصدير مع بيانات وهمية...', 'info');
                
                const mockAnalysis = {
                    vulnerabilities: [],
                    total_vulnerabilities: 0,
                    pages_processed: 1,
                    scan_timestamp: new Date().toISOString()
                };
                
                const mockPageResults = [];
                const mockTargetUrl = 'https://example.com';
                
                try {
                    const report = await core.generateFinalComprehensiveReport(mockAnalysis, mockPageResults, mockTargetUrl);
                    
                    if (report && typeof report === 'string' && report.length > 100) {
                        log(`✅ تم إنشاء التقرير بنجاح - الطول: ${report.length} حرف`, 'success');
                        log('✅ النظام يعمل بشكل صحيح!', 'success');
                        
                        // التحقق من محتوى التقرير
                        if (report.includes('example.com')) {
                            log('✅ التقرير يحتوي على URL الهدف', 'success');
                        }
                        if (report.includes('<html>') || report.includes('<!DOCTYPE')) {
                            log('✅ التقرير بصيغة HTML صحيحة', 'success');
                        }
                        
                    } else {
                        log(`❌ التقرير غير صالح - النوع: ${typeof report}, الطول: ${report ? report.length : 'null'}`, 'error');
                        if (report) {
                            log(`📋 معاينة التقرير: ${report.substring(0, 200)}...`, 'info');
                        }
                    }
                } catch (exportError) {
                    log(`❌ خطأ في التصدير: ${exportError.message}`, 'error');
                    log(`📋 تفاصيل الخطأ: ${exportError.stack}`, 'error');
                }
                
                log('🏁 انتهى الاختبار', 'info');
                
            } catch (error) {
                log(`❌ خطأ عام في الاختبار: ${error.message}`, 'error');
                log(`📋 تفاصيل الخطأ: ${error.stack}`, 'error');
            }
        }
        
        // تشغيل الاختبار تلقائياً عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(runFullTest, 1000);
        });
    </script>
</body>
</html>
